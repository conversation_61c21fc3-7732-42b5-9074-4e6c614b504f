package com.ruoyi.web.controller.system;

import com.alibaba.druid.util.StringUtils;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.system.service.IEmployeeSalesStatisticsService;
import com.ruoyi.system.service.IEnterInformationService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/employee_backend_data")
public class EmployeeBackendDataController {
    @Autowired
    private IEnterInformationService enterInformationService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IEmployeeSalesStatisticsService employeeSalesStatisticsService;

    /**
     * 查询7天每天的开户数量,昨天开始往前推7天,今天的数据不算
     * 查询audit_type为0且update_time在每一天时间范围内的数量
     * @return
     */
    @PostMapping(value = "/querySevenDaysOpenCount")
    public AjaxResult querySevenDaysOpenCount() {
        try {
            List<Map<String, Object>> result = enterInformationService.querySevenDaysOpenCount();
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("查询7天开户数量失败：" + e.getMessage());
        }
    }

    /**
     * 查询7天每天的订单交易总额,昨天开始往前推7天,今天的数据不算
     * 查询order_status为1且order_time在每一天时间范围内的订单的pay_price(支付金额)总和
     * @return
     */
    @PostMapping(value = "/querySevenDaysOrderAmount")
    public AjaxResult querySevenDaysOrderAmount() {
        try {
            List<Map<String, Object>> result = enterInformationService.querySevenDaysOrderAmount();
            return AjaxResult.success(result);
        } catch (Exception e) {
            return AjaxResult.error("查询7天订单交易总额失败：" + e.getMessage());
        }
    }
    @PostMapping(value = "/queryDeptOrderAmount")
    public AjaxResult queryDeptOrderAmount() {
        Long deptId = 104L;
        List<SysUser> sysUsers = userService.selectUserListByDeptId(deptId);
        //去除dept对象中deptName为商务部的数据
        sysUsers.removeIf(sysUser -> sysUser.getDept()!=null && StringUtils.equals("商务部", sysUser.getDept().getDeptName()));
        //然后根据SysUser中的dept对象中deptName将用户分组,分组list中只需保存userId就可以. 用户将会被分为商务一部,商务二部,商务三部和商务四部
        Map<String, List<Long>> deptUserIdsMap = sysUsers.stream().collect(Collectors.groupingBy(sysUser -> sysUser.getDept().getDeptName(), Collectors.mapping(SysUser::getUserId, Collectors.toList())));
        //创建统计时间范围,昨天,7天,本月,本年
        Calendar calendar = getZeroTime();
        Date todayZero = calendar.getTime();
        //创建昨天0点0分0秒
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterdayZero = calendar.getTime();
        calendar = getZeroTime();
        //创建距离今日0点7天的时间
        calendar.add(Calendar.DAY_OF_MONTH, -7);
        Date sevenDaysAgo = calendar.getTime();
        //创建这个月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisMonthBeginTime = calendar.getTime();
        //创建本年1月1号0点0分0秒
        calendar = getZeroTime();
        calendar.set(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date thisYearBeginTime = calendar.getTime();
        //创建统计时间范围,昨天,7天,本月,本年的开始结束时间对;statType为'daily','weekly','monthly','yearly'
        Map<String, Date[]> timeRangeMap = new HashMap<>();
        timeRangeMap.put("daily", new Date[]{yesterdayZero, todayZero});
        timeRangeMap.put("weekly", new Date[]{sevenDaysAgo, todayZero});
        timeRangeMap.put("monthly", new Date[]{thisMonthBeginTime, todayZero});
        timeRangeMap.put("yearly", new Date[]{thisYearBeginTime, todayZero});
        // 存储最终结果
        Map<String, Map<String, Object>> resultMap = new HashMap<>();

        // 先按timeRangeMap循环，计算每个时间范围的总交易额
        Map<String, BigDecimal> totalAmountMap = new HashMap<>();
        for (Map.Entry<String, Date[]> entry1 : timeRangeMap.entrySet()) {
            String statType = entry1.getKey();
            Date[] timeRange = entry1.getValue();
            Date startTime = timeRange[0];
            Date endTime = timeRange[1];

            // 查询该时间范围的总交易额
            BigDecimal totalAmount = enterInformationService.queryOrderAmountByTimeRange(startTime, endTime);
            totalAmountMap.put(statType, totalAmount != null ? totalAmount : BigDecimal.ZERO);
        }

        // 再按部门deptUserIdsMap循环,再按timeRangeMap循环
        for (Map.Entry<String, List<Long>> entry : deptUserIdsMap.entrySet()) {
            String deptName = entry.getKey();
            List<Long> userIdList = entry.getValue();

            Map<String, Object> deptData = new HashMap<>();
            BigDecimal deptTotalDaily = BigDecimal.ZERO;
            BigDecimal deptTotalWeekly = BigDecimal.ZERO;
            BigDecimal deptTotalMonthly = BigDecimal.ZERO;
            BigDecimal deptTotalYearly = BigDecimal.ZERO;

            for (Map.Entry<String, Date[]> entry1 : timeRangeMap.entrySet()) {
                String statType = entry1.getKey();

                // 查询该部门员工在该时间范围的交易额
                BigDecimal deptAmount = employeeSalesStatisticsService.sumTotalSalesByUserIdsAndStatType(userIdList, statType);
                deptAmount = deptAmount != null ? deptAmount : BigDecimal.ZERO;

                deptData.put(statType, deptAmount);

                // 累加到对应的总额中
                switch (statType) {
                    case "daily":
                        deptTotalDaily = deptTotalDaily.add(deptAmount);
                        break;
                    case "weekly":
                        deptTotalWeekly = deptTotalWeekly.add(deptAmount);
                        break;
                    case "monthly":
                        deptTotalMonthly = deptTotalMonthly.add(deptAmount);
                        break;
                    case "yearly":
                        deptTotalYearly = deptTotalYearly.add(deptAmount);
                        break;
                }
            }

            resultMap.put(deptName, deptData);
        }

        // 计算其他交易额：总交易额 - 全部部门交易额之和
        Map<String, Object> otherData = new HashMap<>();
        BigDecimal otherDaily = totalAmountMap.get("daily");
        BigDecimal otherWeekly = totalAmountMap.get("weekly");
        BigDecimal otherMonthly = totalAmountMap.get("monthly");
        BigDecimal otherYearly = totalAmountMap.get("yearly");

        // 减去所有部门的交易额
        for (Map.Entry<String, Map<String, Object>> entry : resultMap.entrySet()) {
            Map<String, Object> deptData = entry.getValue();
            otherDaily = otherDaily.subtract((BigDecimal) deptData.get("daily"));
            otherWeekly = otherWeekly.subtract((BigDecimal) deptData.get("weekly"));
            otherMonthly = otherMonthly.subtract((BigDecimal) deptData.get("monthly"));
            otherYearly = otherYearly.subtract((BigDecimal) deptData.get("yearly"));
        }

        otherData.put("daily", otherDaily);
        otherData.put("weekly", otherWeekly);
        otherData.put("monthly", otherMonthly);
        otherData.put("yearly", otherYearly);
        resultMap.put("其他", otherData);

        // 添加总交易额信息
        Map<String, Object> finalResult = new HashMap<>();
        finalResult.put("totalAmount", totalAmountMap);
        finalResult.put("deptAmount", resultMap);

        return AjaxResult.success(finalResult);
    }


    private static Calendar getZeroTime() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }
}
